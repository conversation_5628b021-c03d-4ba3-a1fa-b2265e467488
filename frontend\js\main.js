/**
 * ReplyPal - Main JavaScript
 * Handles core functionality and initialization
 */

// Global state
let currentResponse = '';
let isGenerating = false;
let isEditMode = false;
let selectedText = '';
let userSubscriptionTier = 'free'; // Default to free tier

// DOM Elements
const selectedTextContainer = document.getElementById('selected-text-container');
const selectedTextArea = document.getElementById('selected-text');
const userIntentArea = document.getElementById('user-intent');
const toneSelect = document.getElementById('tone');
const purposeContainer = document.getElementById('purpose-container');
const purposeSelect = document.getElementById('purpose');
const generateBtn = document.getElementById('generate-btn');
const generateBtnSpinner = document.querySelector('.generate-btn-spinner');
const responseContainer = document.getElementById('response-container');
const responseText = document.getElementById('response-text');
const editBtn = document.getElementById('edit-btn');
const regenerateBtn = document.getElementById('regenerate-btn');
const copyBtn = document.getElementById('copy-btn');
const upgradeBtn = document.getElementById('upgrade-btn');
const loadingSpinner = document.getElementById('loading-spinner');

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Set up event listeners
  setupEventListeners();

  // Initialize UI state
  initializeUI();

  // Check for selected text
  checkForSelectedText();
});

/**
 * Handle keyboard shortcuts within the popup
 */
function handleKeyboardShortcuts(e) {
  // Ctrl+Enter to generate response from anywhere in the popup
  if (e.ctrlKey && e.key === 'Enter') {
    e.preventDefault();
    console.log('Keyboard shortcut detected: Ctrl+Enter');
    generateResponse();
    return;
  }

  // Escape to close the popup
  if (e.key === 'Escape') {
    e.preventDefault();
    console.log('Keyboard shortcut detected: Escape');
    closePopup();
    return;
  }

  // Only process the following shortcuts if we have a response
  if (currentResponse && !responseContainer.classList.contains('hidden')) {
    // Ctrl+C to copy response
    if (e.ctrlKey && e.key === 'c' && document.activeElement === responseText) {
      // Let the browser handle the copy if text is selected
      if (window.getSelection().toString()) {
        return;
      }
      e.preventDefault();
      console.log('Keyboard shortcut detected: Ctrl+C');
      copyToClipboard(currentResponse);
      return;
    }

    // Ctrl+E to edit response
    if (e.ctrlKey && e.key === 'e') {
      e.preventDefault();
      console.log('Keyboard shortcut detected: Ctrl+E');
      toggleEditMode();
      return;
    }

    // Ctrl+R to regenerate response
    if (e.ctrlKey && e.key === 'r') {
      e.preventDefault();
      console.log('Keyboard shortcut detected: Ctrl+R');
      regenerateResponse();
      return;
    }
  }
}

/**
 * Close the popup
 */
function closePopup() {
  // Send message to parent window to close the popup
  if (window.parent && window !== window.parent) {
    window.parent.postMessage({ action: 'closePopup' }, '*');
  } else {
    // If not in iframe, close the window
    window.close();
  }
}

/**
 * Set up event listeners for UI interactions
 */
function setupEventListeners() {
  // Tab navigation
  document.querySelectorAll('.tab-btn, .tab-icon-btn').forEach(button => {
    button.addEventListener('click', () => {
      const tabName = button.dataset.tab;
      switchTab(tabName);
    });
  });

  // Close button
  const closeBtn = document.querySelector('.header-close-btn');
  if (closeBtn) {
    closeBtn.addEventListener('click', closePopup);
  }

  // Generate response
  generateBtn.addEventListener('click', generateResponse);

  // Response actions
  editBtn.addEventListener('click', toggleEditMode);
  regenerateBtn.addEventListener('click', regenerateResponse);
  copyBtn.addEventListener('click', () => copyToClipboard(currentResponse));

  // Global keyboard shortcuts
  document.addEventListener('keydown', handleKeyboardShortcuts);

  // Auto-resize textareas
  setupAutoResizeTextareas();
}

/**
 * Initialize UI state
 */
function initializeUI() {
  // Load settings
  loadSettings();

  // Load history
  loadHistory();

  // Set up message listener for parent window communication
  setupMessageListener();

  // Check user subscription status and update UI
  checkUserSubscription();

  // Check usage limits and update UI
  checkUsageLimits();

  // Set up upgrade button click handler
  if (upgradeBtn) {
    upgradeBtn.addEventListener('click', handleUpgradeClick);
  }
}

/**
 * Check for selected text and update UI accordingly
 */
function checkForSelectedText() {
  // Check if we're in an iframe
  const isInIframe = window !== window.top;

  if (isInIframe) {
    // We're in an iframe, get selected text from storage
    chrome.storage.local.get('selectedText', (data) => {
      selectedText = data.selectedText || '';
      updateUIForSelectedText();
    });
  } else {
    // We're not in an iframe, check for selected text in the current window
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      selectedText = selection.toString().trim();
    }
    updateUIForSelectedText();
  }
}

/**
 * Update UI based on whether text is selected
 */
function updateUIForSelectedText() {
  const userIntentLabel = document.querySelector('label[for="user-intent"]');

  // Get the info icon element
  const userIntentInfoIcon = document.querySelector('label[for="user-intent"] .info-icon');

  if (selectedText) {
    // Text is selected, show the selected text container
    selectedTextContainer.classList.remove('hidden');
    selectedTextArea.value = selectedText;

    // Show purpose dropdown
    purposeContainer.classList.remove('hidden');

    // Update user intent label and placeholder for selected text mode
    if (userIntentLabel) {
      userIntentLabel.childNodes[0].nodeValue = 'Your Intent ';
    }
    var content = "Optional: Describe what you want to say, like 'Reply politely that I cannot attend the event' or 'Write a thank you note'. If you leave it blank, the AI will choose a response based on the selected text, tone, and purpose. Your input will be prioritized.";

    userIntentArea.placeholder = content;
    userIntentArea.title = content;

    // Update the info icon title to match the textarea
    if (userIntentInfoIcon) {
      userIntentInfoIcon.title = content;
    }
  } else {
    // No text selected, hide the selected text container
    selectedTextContainer.classList.add('hidden');

    // Hide purpose dropdown
    purposeContainer.classList.add('hidden');

    // Update user intent label and placeholder for no selected text mode
    if (userIntentLabel) {
      userIntentLabel.childNodes[0].nodeValue = 'Your Message ';
    }
    var content = "Required: Describe what you want the AI to generate, like 'Write a thank you email to my team', 'Create a LinkedIn post about my promotion', 'How do I politely decline a meeting?' or 'Write a whatsapp message to my relative to invite them to dinner.' Your input and the tone guides the response.";
    userIntentArea.placeholder = content;
    userIntentArea.title = content;

    // Update the info icon title to match the textarea
    if (userIntentInfoIcon) {
      userIntentInfoIcon.title = content;
    }
  }

  // Focus on user intent
  userIntentArea.focus();
}

/**
 * Generate a response based on user input
 */
async function generateResponse() {
  // Validate input
  if (!validateInput()) {
    return;
  }

  // Don't allow multiple generations at once
  if (isGenerating) {
    return;
  }

  // Check if the button is disabled due to usage limits
  if (generateBtn.disabled && generateBtn.classList.contains('disabled') &&
      generateBtn.title.includes('Daily usage limit reached')) {
    // Only hide the response container if there's no current response
    // This ensures that the last (5th) response remains visible even after reaching the limit
    if (responseContainer && !currentResponse) {
      responseContainer.classList.add('hidden');
    }
    showToast('Daily usage limit reached. Consider upgrading for more responses.', true);
    return;
  }

  // Set generating state
  isGenerating = true;
  generateBtn.disabled = true;

  // Show loading spinner
  showLoadingOverlay();

  try {
    // Get request data
    const requestData = {
      selected_text: selectedTextArea.value,
      user_intent: userIntentArea.value,
      tone: toneSelect.value,
      purpose: purposeSelect.value
    };

    // Use streaming API
    await generateStreamingResponse(requestData);

    // Save to history if enabled
    saveToHistory(requestData.user_intent, requestData.selected_text, currentResponse);

    // Show response container
    responseContainer.classList.remove('hidden');

    // Scroll to response
    scrollToResponse();

    // Refresh usage information
    if (typeof fetchUsageInfo === 'function') {
      fetchUsageInfo();
    }

    // Also refresh the usage limits in the compose tab
    checkUsageLimits();
  } catch (error) {
    // Don't log to console, handle in UI instead

    // Check if this is a usage limit exceeded error (429)
    if (error.message &&
        (error.message.includes('429') || error.message.toLowerCase().includes('exceeded your daily usage limit'))) {
      // Only hide the response container if there's no current response
      // This ensures that the last (5th) response remains visible even after reaching the limit
      if (responseContainer && !currentResponse) {
        responseContainer.classList.add('hidden');
      }

      // Show a toast notification
      showToast('Daily usage limit reached. Consider upgrading for more responses.', true);

      // Update usage limits to reflect that we've reached the limit
      updateComposeUsageUI(0, 5);

      // Also update the settings tab usage info if the function exists
      if (typeof updateUsageUI === 'function') {
        updateUsageUI(0, 5);
      }
    } else if (error.message && (error.message.includes('401') || error.message.toLowerCase().includes('not authenticated'))) {
      // Authentication error
      showAuthenticationErrorMessage();

      // Show response container for authentication errors
      responseContainer.classList.remove('hidden');
    } else {
      // Show regular error message in the UI (not console)
      showToast(`Error: ${error.message}`, true);
      showErrorInResponseArea(error.message);

      // Show response container for regular errors (not usage limit errors)
      responseContainer.classList.remove('hidden');
    }
  } finally {
    // Reset generating state
    isGenerating = false;
    generateBtn.disabled = false;

    // Hide loading spinner
    hideLoadingOverlay();
  }
}

/**
 * Validate user input before generating response
 */
function validateInput() {
  // User intent is required only if selected text is empty
  if (!userIntentArea.value.trim() && !selectedTextArea.value.trim()) {
    // Show different error message based on whether selected text is visible
    if (selectedTextContainer.classList.contains('hidden')) {
      showToast('Please enter your message', true);
    } else {
      showToast('Please enter your intent', true);
    }
    userIntentArea.focus();
    return false;
  }
  return true;
}

/**
 * Generate a streaming response
 */
async function generateStreamingResponse(requestData) {
  // Reset response
  setResponseText('');
  currentResponse = '';

  // Set up streaming
  const isInIframe = window !== window.top;

  if (isInIframe) {
    // We're in an iframe, use postMessage to communicate with parent
    await streamFromParent(requestData);
  } else {
    // We're not in an iframe, use fetch directly
    await streamFromAPI(requestData);
  }
}

/**
 * Stream response from parent window
 */
async function streamFromParent(requestData) {
  return new Promise((resolve, reject) => {
    const messageHandler = (event) => {
      if (event.source !== window.parent) return;

      const message = event.data;

      if (message.action === 'streamResponseChunk') {
        if (message.chunk === '[DONE]') {
          window.removeEventListener('message', messageHandler);
          resolve();
        } else {
          appendResponseChunk(message.chunk);
        }
      } else if (message.action === 'streamResponseError') {
        window.removeEventListener('message', messageHandler);
        reject(new Error(message.error || 'Unknown streaming error'));
      }
    };

    window.addEventListener('message', messageHandler);

    // Send request to parent
    window.parent.postMessage({
      action: 'generateStreamingResponse',
      data: requestData
    }, '*');

    // Set timeout
    setTimeout(() => {
      window.removeEventListener('message', messageHandler);
      if (currentResponse.length === 0) {
        reject(new Error('Request timed out'));
      } else {
        resolve();
      }
    }, 60000);
  });
}

/**
 * Stream response directly from API
 */
async function streamFromAPI(requestData) {
  try {
    // Get API URL from settings
    const settings = await getSettings();
    const environment = settings.environment || 'prod';
    const apiUrls = {
      local: 'http://localhost:8000',
      dev: 'https://dev-api.replypal.com',
      prod: 'https://n9dk65q3fd.execute-api.us-east-1.amazonaws.com/production'
    };
    const apiUrl = apiUrls[environment] || apiUrls.prod;

    // Get user data for authentication
    const userData = await getUserData();
    const authToken = userData?.apiToken;

    console.log('User data for API request:', userData ? 'Found' : 'Not found');
    console.log('Auth token for API request:', authToken ? 'Found' : 'Not found');

    // Prepare headers
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    };

    // Add authorization header if token exists
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
      console.log('Added Authorization header to request');
    } else {
      console.warn('No auth token available for API request');
    }

    // Set up fetch with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);

    const response = await fetch(`${apiUrl}/generate_stream`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    // Process the stream
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.startsWith('data: ')) {
          const data = line.substring(6);
          if (data === '[DONE]') return;
          appendResponseChunk(data);
        }
      }
    }
  } catch (error) {
    // Don't log to console, just throw the error to be handled by the parent function
    throw error;
  }
}

/**
 * Append a chunk to the response
 */
function appendResponseChunk(chunk) {
  // Append chunk to response
  currentResponse += chunk;
  setResponseText(currentResponse);

  // Auto-scroll textarea to bottom
  responseText.scrollTop = responseText.scrollHeight;
}

/**
 * Toggle edit mode for response
 */
function toggleEditMode() {
  if (!currentResponse) return;

  if (isEditMode) {
    // Save changes
    saveResponseChanges();
  } else {
    // Enter edit mode
    enterEditMode();
  }
}

/**
 * Enter edit mode for response
 */
function enterEditMode() {
  // Set edit mode flag
  isEditMode = true;

  // Update button text
  editBtn.innerHTML = '<i class="fa-solid fa-floppy-disk"></i> Save';
  editBtn.title = 'Save Changes';

  // Make response editable
  responseText.removeAttribute('readonly');
  responseText.classList.add('editing');

  // Disable other buttons
  regenerateBtn.disabled = true;
  regenerateBtn.classList.add('disabled');
  copyBtn.disabled = true;
  copyBtn.classList.add('disabled');

  // Focus on response text
  responseText.focus();
}

/**
 * Save response changes
 */
function saveResponseChanges() {
  // Update current response
  currentResponse = responseText.innerText;

  // Make response readonly again
  responseText.setAttribute('readonly', 'readonly');
  responseText.classList.remove('editing');

  // Reset edit mode flag
  isEditMode = false;

  // Update button text
  editBtn.innerHTML = '<i class="fa-solid fa-pen-to-square"></i> Edit';
  editBtn.title = 'Edit Response';

  // Re-enable other buttons
  regenerateBtn.disabled = false;
  regenerateBtn.classList.remove('disabled');
  copyBtn.disabled = false;
  copyBtn.classList.remove('disabled');

  // Show success message
  showToast('Changes saved');

  // Update history if needed
  updateLastHistoryItem(currentResponse);
}

/**
 * Regenerate the response
 */
function regenerateResponse() {
  // If in edit mode, exit it first
  if (isEditMode) {
    isEditMode = false;
    editBtn.innerHTML = '<i class="fa-solid fa-pen-to-square"></i> Edit';
    editBtn.title = 'Edit Response';
    responseText.setAttribute('readonly', 'readonly');
    responseText.classList.remove('editing');
    regenerateBtn.disabled = false;
    regenerateBtn.classList.remove('disabled');
    copyBtn.disabled = false;
    copyBtn.classList.remove('disabled');
  }

  // Generate a new response
  generateResponse();
}

/**
 * Set up message listener for parent window communication
 */
function setupMessageListener() {
  window.addEventListener('message', (event) => {
    // Only accept messages from parent window
    if (event.source !== window.parent) return;

    const message = event.data;

    // Handle clipboard operation result
    if (message.action === 'copyToClipboardResult') {
      if (message.success) {
        showToast('Copied to clipboard!');
      } else {
        showToast('Failed to copy to clipboard', true);
      }
    }
  });
}

/**
 * Set response text
 */
function setResponseText(text) {
  const responseDiv = document.getElementById('response-text');
  if (responseDiv) {
    // Display as HTML, so <br> and other tags are rendered
    responseDiv.innerHTML = text || '';
  }
}

/**
 * Show a user-friendly message when the daily usage limit is exceeded
 * with an option to upgrade to the Basic tier
 */
function showUsageLimitExceededMessage() {
  // Only hide the response container if there's no current response
  // This ensures that the last (5th) response remains visible even after reaching the limit
  if (responseContainer && !currentResponse) {
    responseContainer.classList.add('hidden');
  }

  // Show a toast notification
  showToast('Daily usage limit reached. Consider upgrading for more responses.', true);
}

/**
 * Show a user-friendly message when authentication fails
 */
function showAuthenticationErrorMessage() {
  // Create a user-friendly message with login option
  const message = `
    <div class="error-message">
      <h3>Authentication Error</h3>
      <p>Your session may have expired or you need to log in again.</p>
      <button id="login-again-btn" class="primary-btn">
        <i class="fa-solid fa-sign-in-alt"></i> Log In Again
      </button>
    </div>
  `;

  // Set the message in the response area
  setResponseText(message);

  // Show a toast notification
  showToast('Authentication error. Please log in again.', true);

  // Add event listener to the login button
  setTimeout(() => {
    const loginBtn = document.getElementById('login-again-btn');
    if (loginBtn) {
      loginBtn.addEventListener('click', () => {
        // Redirect to login page
        chrome.runtime.sendMessage({ action: 'signOut' }, () => {
          // Reload the popup to show login page
          window.location.href = 'login.html';
        });
      });
    }
  }, 100);
}

/**
 * Show an error message in the response area
 * @param {string} errorMessage - The error message to display
 */
function showErrorInResponseArea(errorMessage) {
  // Create a user-friendly error message
  const message = `
    <div class="error-message">
      <h3>Error</h3>
      <p>${errorMessage}</p>
      <button id="try-again-btn" class="primary-btn">
        <i class="fa-solid fa-redo"></i> Try Again
      </button>
    </div>
  `;

  // Set the message in the response area
  setResponseText(message);

  // Add event listener to the try again button
  setTimeout(() => {
    const tryAgainBtn = document.getElementById('try-again-btn');
    if (tryAgainBtn) {
      tryAgainBtn.addEventListener('click', regenerateResponse);
    }
  }, 100);
}

/**
 * Initiate the subscription upgrade process
 */
async function initiateSubscriptionUpgrade() {
  try {
    // Show loading state
    showToast('Preparing upgrade options...', false);

    // Get user data for authentication
    const userData = await getUserData();

    // Get settings for API URL
    const settings = await getSettings();
    const environment = settings.environment || 'local';

    // If user is logged in, pass login details to subscription page
    if (userData && userData.apiToken) {
      // Store user data in localStorage for the subscription page
      localStorage.setItem('replypal_user', JSON.stringify(userData));
      localStorage.setItem('replypal_environment', environment);
      // Mark that we're coming from the extension
      localStorage.setItem('replypal_from_extension', 'true');
    }

    // Open subscription page in a new tab
    // The subscriptionUrl is defined in settings.js
    window.open(subscriptionUrl, '_blank');
    showToast('Opening subscription page...', false);
  } catch (error) {
    console.error('Error initiating subscription upgrade:', error);
    showToast(`Error: ${error.message}`, true);
  }
}

function copyToClipboard(text) {
  if (!text) return;
  // Use the Clipboard API if available
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(text)
      .then(() => showToast('Copied to clipboard!'))
      .catch(() => showToast('Failed to copy to clipboard', true));
  } else {
    // Fallback for older browsers
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.setAttribute('readonly', '');
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    document.body.appendChild(textarea);
    textarea.select();
    try {
      document.execCommand('copy');
      showToast('Copied to clipboard!');
    } catch (err) {
      showToast('Failed to copy to clipboard', true);
    }
    document.body.removeChild(textarea);
  }
}

/**
 * Get settings from storage
 */
function getSettings() {
  return new Promise((resolve) => {
    chrome.storage.local.get('settings', (data) => {
      resolve(data.settings || {
        environment: 'prod',
        saveHistory: true,
        useMockApi: false
      });
    });
  });
}

/**
 * Get user data from storage
 */
function getUserData() {
  return new Promise((resolve) => {
    chrome.storage.local.get('user', (data) => {
      resolve(data.user || null);
    });
  });
}

/**
 * Check user subscription status and update UI accordingly
 */
async function checkUserSubscription() {
  try {
    // Get user data
    const userData = await getUserData();

    if (!userData || !userData.apiToken) {
      // User is not logged in, assume free tier
      userSubscriptionTier = 'free';
      showUpgradeButton();
      return;
    }

    // Get settings for API URL
    const settings = await getSettings();
    const environment = settings.environment || 'prod';
    const apiUrls = {
      local: 'http://localhost:8000',
      dev: 'https://dev-api.replypal.com',
      prod: 'https://n9dk65q3fd.execute-api.us-east-1.amazonaws.com/production'
    };
    const apiUrl = apiUrls[environment] || apiUrls.prod;

    // Fetch user subscription info
    const response = await fetch(`${apiUrl}/subscription/info`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userData.apiToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      // Error fetching subscription info, assume free tier
      userSubscriptionTier = 'free';
      showUpgradeButton();
      return;
    }

    const subscriptionData = await response.json();
    userSubscriptionTier = subscriptionData.tier || 'free';

    // Show upgrade button only for free tier users
    if (userSubscriptionTier === 'free') {
      showUpgradeButton();
    } else {
      hideUpgradeButton();
    }
  } catch (error) {
    console.error('Error checking subscription status:', error);
    // Assume free tier on error
    userSubscriptionTier = 'free';
    showUpgradeButton();
  }
}

/**
 * Show the upgrade button in the header
 */
function showUpgradeButton() {
  if (upgradeBtn) {
    upgradeBtn.style.display = 'flex';
  }
}

/**
 * Hide the upgrade button in the header
 */
function hideUpgradeButton() {
  if (upgradeBtn) {
    upgradeBtn.style.display = 'none';
  }
}

/**
 * Check usage limits and update UI accordingly
 */
async function checkUsageLimits() {
  try {
    // Get user data for authentication
    const userData = await getUserData();

    if (!userData || !userData.apiToken) {
      // User is not logged in, show default values
      updateComposeUsageUI(0, 5);
      return;
    }

    // Get settings for API URL
    const settings = await getSettings();
    const environment = settings.environment || 'prod';
    const apiUrls = {
      local: 'http://localhost:8000',
      dev: 'https://dev-api.replypal.com',
      prod: 'https://n9dk65q3fd.execute-api.us-east-1.amazonaws.com/production'
    };
    const apiUrl = apiUrls[environment] || apiUrls.prod;

    // Fetch usage information
    const response = await fetch(`${apiUrl}/subscription/usage`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userData.apiToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      // Error fetching usage info, show default values
      updateComposeUsageUI(0, 5);
      return;
    }

    const usageData = await response.json();
    const dailyUsage = usageData.daily_usage || 0;
    const dailyLimit = usageData.daily_limit || 5;
    const remaining = Math.max(0, dailyLimit - dailyUsage);

    // Update UI with usage information
    updateComposeUsageUI(remaining, dailyLimit);
  } catch (error) {
    console.error('Error fetching usage information:', error);
    // Show default values on error
    updateComposeUsageUI(0, 5);
  }
}

/**
 * Update the compose tab UI with usage information
 * @param {number} remaining - Remaining responses
 * @param {number} total - Total allowed responses
 */
function updateComposeUsageUI(remaining, total) {
  // Update generate button state
  if (generateBtn) {
    // Get the generate container
    const generateContainer = generateBtn.parentNode;

    // Remove any existing usage indicators
    const existingLimitIndicator = document.getElementById('compose-limit-indicator');
    if (existingLimitIndicator) {
      existingLimitIndicator.remove();
    }

    const existingUsageCounter = document.getElementById('compose-usage-counter');
    if (existingUsageCounter) {
      existingUsageCounter.remove();
    }

    const existingCompactIndicator = document.getElementById('compact-usage-indicator');
    if (existingCompactIndicator) {
      existingCompactIndicator.remove();
    }

    if (remaining <= 0) {
      // Disable generate button
      generateBtn.disabled = true;
      generateBtn.classList.add('disabled');
      generateBtn.title = 'Daily usage limit reached. Upgrade for more responses.';

      // Also add the limit indicator next to the button for visibility
      let limitIndicator = document.createElement('div');
      limitIndicator.id = 'compose-limit-indicator';
      limitIndicator.className = 'compose-limit-indicator';
      limitIndicator.innerHTML = `
        <i class="fa-solid fa-exclamation-circle"></i>
        <span>Daily limit reached</span>
      `;
      // Add click handler to show upgrade message
      limitIndicator.addEventListener('click', () => {
        // Just show the toast notification, don't show the response area
        showToast('Daily usage limit reached. Consider upgrading for more responses.', true);
      });

      // Insert after the generate button
      generateContainer.insertBefore(limitIndicator, generateBtn.nextSibling);

      // Only hide the response container if it's visible AND we don't have a current response
      // This ensures that the last (5th) response remains visible even after reaching the limit
      if (responseContainer && !responseContainer.classList.contains('hidden') && !currentResponse) {
        responseContainer.classList.add('hidden');
      }
    } else {
      // Enable generate button
      generateBtn.disabled = false;
      generateBtn.classList.remove('disabled');
      generateBtn.title = 'Generate response';

      // Create compact indicator above the button
      const compactIndicator = document.createElement('div');
      compactIndicator.id = 'compact-usage-indicator';
      compactIndicator.className = 'compact-usage-indicator';

      // Add warning class if running low on responses
      if (remaining <= Math.ceil(total * 0.2)) {
        compactIndicator.classList.add('warning');
      }

      compactIndicator.innerHTML = `${remaining}/${total} responses left today`;

      // Add click handler to show subscription info
      compactIndicator.addEventListener('click', () => {
        // Switch to settings tab to show usage info
        switchTab('settings');

        // Scroll to usage section
        const usageContainer = document.getElementById('usage-container');
        if (usageContainer) {
          usageContainer.scrollIntoView({ behavior: 'smooth' });
        }
      });

      // Insert before the generate button
      generateContainer.insertBefore(compactIndicator, generateBtn);
    }
  }
}

/**
 * Handle upgrade button click
 */
async function handleUpgradeClick() {
  try {
    // Get user data
    const userData = await getUserData();

    // Get settings for API URL
    const settings = await getSettings();
    const environment = settings.environment || 'local';

    // If user is logged in, pass login details to subscription page
    if (userData && userData.apiToken) {
      // Store user data in localStorage for the subscription page
      localStorage.setItem('replypal_user', JSON.stringify(userData));
      localStorage.setItem('replypal_environment', environment);
      // Mark that we're coming from the extension
      localStorage.setItem('replypal_from_extension', 'true');
    }

    // Open subscription page in a new tab
    // The subscriptionUrl is defined in settings.js
    window.open(subscriptionUrl, '_blank');

    // Show toast notification
    showToast('Opening subscription page...', false);
  } catch (error) {
    console.error('Error handling upgrade click:', error);
    showToast('Error opening subscription page', true);
  }
}